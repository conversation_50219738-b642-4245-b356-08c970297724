<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Management System - Technical Documentation</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Prism.js for syntax highlighting -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    
    <style>
        :root {
            /* ERDB Brand Colors */
            --primary-dark-green: #378C47;
            --secondary-dark-blue: #0267B6;
            --light-green: #5BA85B;
            --light-blue: #3CA6D6;
            --orange: #FFBD5C;
            --orange-dark: #FC762B;
            --red: #C12323;
            --dark: #000000;
            --light: #f8f9fa;
            --gray-100: #f8f9fa;
            --gray-200: #e9ecef;
            --gray-300: #dee2e6;
            --gray-600: #6c757d;
            --gray-700: #495057;
            --gray-800: #343a40;
            --gray-900: #212529;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--gray-800);
            background-color: var(--light);
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-dark-green), var(--secondary-dark-blue));
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .sidebar {
            background-color: var(--gray-100);
            border-right: 1px solid var(--gray-300);
            height: calc(100vh - 76px);
            overflow-y: auto;
            position: sticky;
            top: 76px;
        }

        .sidebar .nav-link {
            color: var(--gray-700);
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.25rem 0.5rem;
            transition: all 0.2s ease;
        }

        .sidebar .nav-link:hover {
            background-color: var(--light-green);
            color: white;
            transform: translateX(5px);
        }

        .sidebar .nav-link.active {
            background-color: var(--primary-dark-green);
            color: white;
        }

        .sidebar .nav-link i {
            width: 20px;
            margin-right: 0.5rem;
        }

        .content-area {
            padding: 2rem;
            background-color: white;
            min-height: calc(100vh - 76px);
        }

        .section-header {
            background: linear-gradient(135deg, var(--primary-dark-green), var(--light-green));
            color: white;
            padding: 1.5rem;
            border-radius: 0.5rem;
            margin-bottom: 2rem;
        }

        .section-header h1 {
            margin: 0;
            font-weight: 700;
        }

        .section-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }

        .card {
            border: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-radius: 0.75rem;
            margin-bottom: 1.5rem;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, var(--gray-100), var(--gray-200));
            border-bottom: 1px solid var(--gray-300);
            font-weight: 600;
            color: var(--gray-800);
        }

        .badge-primary {
            background-color: var(--primary-dark-green);
        }

        .badge-secondary {
            background-color: var(--secondary-dark-blue);
        }

        .badge-success {
            background-color: var(--light-green);
        }

        .badge-info {
            background-color: var(--light-blue);
        }

        .badge-warning {
            background-color: var(--orange);
            color: var(--gray-800);
        }

        .badge-danger {
            background-color: var(--red);
        }

        .btn-primary {
            background-color: var(--primary-dark-green);
            border-color: var(--primary-dark-green);
        }

        .btn-primary:hover {
            background-color: var(--light-green);
            border-color: var(--light-green);
        }

        .btn-secondary {
            background-color: var(--secondary-dark-blue);
            border-color: var(--secondary-dark-blue);
        }

        .btn-secondary:hover {
            background-color: var(--light-blue);
            border-color: var(--light-blue);
        }

        .table th {
            background-color: var(--gray-100);
            border-color: var(--gray-300);
            font-weight: 600;
            color: var(--gray-800);
        }

        .table td {
            border-color: var(--gray-200);
        }

        .code-block {
            background-color: var(--gray-900);
            color: var(--light);
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            margin: 1rem 0;
        }

        .architecture-diagram {
            background: linear-gradient(135deg, var(--gray-100), white);
            border: 2px solid var(--gray-300);
            border-radius: 0.75rem;
            padding: 2rem;
            margin: 2rem 0;
            text-align: center;
        }

        .component-box {
            background-color: var(--primary-dark-green);
            color: white;
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 0.5rem;
            display: inline-block;
            min-width: 150px;
            font-weight: 600;
        }

        .component-box.secondary {
            background-color: var(--secondary-dark-blue);
        }

        .component-box.success {
            background-color: var(--light-green);
        }

        .component-box.info {
            background-color: var(--light-blue);
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .feature-card {
            background: white;
            border: 1px solid var(--gray-300);
            border-radius: 0.75rem;
            padding: 1.5rem;
            transition: all 0.2s ease;
        }

        .feature-card:hover {
            border-color: var(--primary-dark-green);
            box-shadow: 0 4px 12px rgba(55, 140, 71, 0.15);
        }

        .feature-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--primary-dark-green), var(--light-green));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .tech-spec-table {
            background-color: var(--gray-50);
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .tech-spec-table th {
            background-color: var(--primary-dark-green);
            color: white;
            font-weight: 600;
        }

        .api-endpoint {
            background-color: var(--gray-100);
            border-left: 4px solid var(--primary-dark-green);
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 0.5rem 0.5rem 0;
        }

        .method-get {
            border-left-color: var(--light-green);
        }

        .method-post {
            border-left-color: var(--secondary-dark-blue);
        }

        .method-put {
            border-left-color: var(--orange);
        }

        .method-delete {
            border-left-color: var(--red);
        }

        .scroll-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background-color: var(--primary-dark-green);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1000;
        }

        .scroll-to-top.visible {
            opacity: 1;
        }

        .scroll-to-top:hover {
            background-color: var(--light-green);
            transform: scale(1.1);
        }

        /* Dark mode styles */
        .dark-mode {
            --light: #1a1a1a;
            --gray-100: #2d2d2d;
            --gray-200: #404040;
            --gray-300: #555555;
            --gray-600: #cccccc;
            --gray-700: #e0e0e0;
            --gray-800: #f0f0f0;
            --gray-900: #ffffff;
        }

        .dark-mode body {
            background-color: var(--light);
            color: var(--gray-800);
        }

        .dark-mode .content-area {
            background-color: var(--gray-100);
        }

        .dark-mode .card {
            background-color: var(--gray-200);
            color: var(--gray-800);
        }

        .dark-mode .sidebar {
            background-color: var(--gray-200);
            border-right-color: var(--gray-300);
        }

        .theme-toggle {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 1001;
            background-color: var(--primary-dark-green);
            color: white;
            border: none;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            background-color: var(--light-green);
            transform: scale(1.1);
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -100%;
                transition: left 0.3s ease;
                z-index: 1000;
                width: 280px;
            }

            .sidebar.show {
                left: 0;
            }

            .content-area {
                padding: 1rem;
            }

            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Theme Toggle Button -->
    <button class="theme-toggle" onclick="toggleTheme()" title="Toggle Dark Mode">
        <i class="fas fa-moon" id="theme-icon"></i>
    </button>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#overview">
                <i class="fas fa-file-alt me-2"></i>
                Document Management System - Technical Documentation
            </a>
            <button class="navbar-toggler d-lg-none" type="button" onclick="toggleSidebar()">
                <span class="navbar-toggler-icon"></span>
            </button>
        </div>
    </nav>
</body>
</html>
