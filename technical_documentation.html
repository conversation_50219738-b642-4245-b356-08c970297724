<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Management System - Technical Documentation</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Prism.js for syntax highlighting -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">

    <style>
        :root {
            /* ERDB Brand Colors */
            --primary-dark-green: #378C47;
            --secondary-dark-blue: #0267B6;
            --light-green: #5BA85B;
            --light-blue: #3CA6D6;
            --orange: #FFBD5C;
            --orange-dark: #FC762B;
            --red: #C12323;
            --dark: #000000;
            --light: #f8f9fa;
            --gray-100: #f8f9fa;
            --gray-200: #e9ecef;
            --gray-300: #dee2e6;
            --gray-600: #6c757d;
            --gray-700: #495057;
            --gray-800: #343a40;
            --gray-900: #212529;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--gray-800);
            background-color: var(--light);
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-dark-green), var(--secondary-dark-blue));
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .sidebar {
            background-color: var(--gray-100);
            border-right: 1px solid var(--gray-300);
            height: calc(100vh - 76px);
            overflow-y: auto;
            position: sticky;
            top: 76px;
        }

        .sidebar .nav-link {
            color: var(--gray-700);
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.25rem 0.5rem;
            transition: all 0.2s ease;
        }

        .sidebar .nav-link:hover {
            background-color: var(--light-green);
            color: white;
            transform: translateX(5px);
        }

        .sidebar .nav-link.active {
            background-color: var(--primary-dark-green);
            color: white;
        }

        .sidebar .nav-link i {
            width: 20px;
            margin-right: 0.5rem;
        }

        .content-area {
            padding: 2rem;
            background-color: white;
            min-height: calc(100vh - 76px);
        }

        .section-header {
            background: linear-gradient(135deg, var(--primary-dark-green), var(--light-green));
            color: white;
            padding: 1.5rem;
            border-radius: 0.5rem;
            margin-bottom: 2rem;
        }

        .section-header h1 {
            margin: 0;
            font-weight: 700;
        }

        .section-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }

        .card {
            border: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-radius: 0.75rem;
            margin-bottom: 1.5rem;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, var(--gray-100), var(--gray-200));
            border-bottom: 1px solid var(--gray-300);
            font-weight: 600;
            color: var(--gray-800);
        }

        .badge-primary {
            background-color: var(--primary-dark-green);
        }

        .badge-secondary {
            background-color: var(--secondary-dark-blue);
        }

        .badge-success {
            background-color: var(--light-green);
        }

        .badge-info {
            background-color: var(--light-blue);
        }

        .badge-warning {
            background-color: var(--orange);
            color: var(--gray-800);
        }

        .badge-danger {
            background-color: var(--red);
        }

        .btn-primary {
            background-color: var(--primary-dark-green);
            border-color: var(--primary-dark-green);
        }

        .btn-primary:hover {
            background-color: var(--light-green);
            border-color: var(--light-green);
        }

        .btn-secondary {
            background-color: var(--secondary-dark-blue);
            border-color: var(--secondary-dark-blue);
        }

        .btn-secondary:hover {
            background-color: var(--light-blue);
            border-color: var(--light-blue);
        }

        .table th {
            background-color: var(--gray-100);
            border-color: var(--gray-300);
            font-weight: 600;
            color: var(--gray-800);
        }

        .table td {
            border-color: var(--gray-200);
        }

        .code-block {
            background-color: var(--gray-900);
            color: var(--light);
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            margin: 1rem 0;
        }

        .architecture-diagram {
            background: linear-gradient(135deg, var(--gray-100), white);
            border: 2px solid var(--gray-300);
            border-radius: 0.75rem;
            padding: 2rem;
            margin: 2rem 0;
            text-align: center;
        }

        .component-box {
            background-color: var(--primary-dark-green);
            color: white;
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 0.5rem;
            display: inline-block;
            min-width: 150px;
            font-weight: 600;
        }

        .component-box.secondary {
            background-color: var(--secondary-dark-blue);
        }

        .component-box.success {
            background-color: var(--light-green);
        }

        .component-box.info {
            background-color: var(--light-blue);
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .feature-card {
            background: white;
            border: 1px solid var(--gray-300);
            border-radius: 0.75rem;
            padding: 1.5rem;
            transition: all 0.2s ease;
        }

        .feature-card:hover {
            border-color: var(--primary-dark-green);
            box-shadow: 0 4px 12px rgba(55, 140, 71, 0.15);
        }

        .feature-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--primary-dark-green), var(--light-green));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .tech-spec-table {
            background-color: var(--gray-50);
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .tech-spec-table th {
            background-color: var(--primary-dark-green);
            color: white;
            font-weight: 600;
        }

        .api-endpoint {
            background-color: var(--gray-100);
            border-left: 4px solid var(--primary-dark-green);
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 0.5rem 0.5rem 0;
        }

        .method-get {
            border-left-color: var(--light-green);
        }

        .method-post {
            border-left-color: var(--secondary-dark-blue);
        }

        .method-put {
            border-left-color: var(--orange);
        }

        .method-delete {
            border-left-color: var(--red);
        }

        .scroll-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background-color: var(--primary-dark-green);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1000;
        }

        .scroll-to-top.visible {
            opacity: 1;
        }

        .scroll-to-top:hover {
            background-color: var(--light-green);
            transform: scale(1.1);
        }

        /* Dark mode styles */
        .dark-mode {
            --light: #1a1a1a;
            --gray-100: #2d2d2d;
            --gray-200: #404040;
            --gray-300: #555555;
            --gray-600: #cccccc;
            --gray-700: #e0e0e0;
            --gray-800: #f0f0f0;
            --gray-900: #ffffff;
        }

        .dark-mode body {
            background-color: var(--light);
            color: var(--gray-800);
        }

        .dark-mode .content-area {
            background-color: var(--gray-100);
        }

        .dark-mode .card {
            background-color: var(--gray-200);
            color: var(--gray-800);
        }

        .dark-mode .sidebar {
            background-color: var(--gray-200);
            border-right-color: var(--gray-300);
        }

        .theme-toggle {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 1001;
            background-color: var(--primary-dark-green);
            color: white;
            border: none;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            background-color: var(--light-green);
            transform: scale(1.1);
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -100%;
                transition: left 0.3s ease;
                z-index: 1000;
                width: 280px;
            }

            .sidebar.show {
                left: 0;
            }

            .content-area {
                padding: 1rem;
            }

            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Theme Toggle Button -->
    <button class="theme-toggle" onclick="toggleTheme()" title="Toggle Dark Mode">
        <i class="fas fa-moon" id="theme-icon"></i>
    </button>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#overview">
                <i class="fas fa-file-alt me-2"></i>
                Document Management System - Technical Documentation
            </a>
            <button class="navbar-toggler d-lg-none" type="button" onclick="toggleSidebar()">
                <span class="navbar-toggler-icon"></span>
            </button>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar Navigation -->
            <nav class="col-lg-3 col-xl-2 sidebar" id="sidebar">
                <div class="nav flex-column pt-3">
                    <a class="nav-link active" href="#overview">
                        <i class="fas fa-home"></i>Overview
                    </a>
                    <a class="nav-link" href="#architecture">
                        <i class="fas fa-sitemap"></i>System Architecture
                    </a>
                    <a class="nav-link" href="#core-features">
                        <i class="fas fa-cogs"></i>Core Features
                    </a>
                    <a class="nav-link" href="#user-management">
                        <i class="fas fa-users"></i>User Management
                    </a>
                    <a class="nav-link" href="#ai-integration">
                        <i class="fas fa-brain"></i>AI Integration
                    </a>
                    <a class="nav-link" href="#geographical">
                        <i class="fas fa-map-marked-alt"></i>Geographical Features
                    </a>
                    <a class="nav-link" href="#database">
                        <i class="fas fa-database"></i>Database Schema
                    </a>
                    <a class="nav-link" href="#api">
                        <i class="fas fa-code"></i>API Documentation
                    </a>
                    <a class="nav-link" href="#analytics">
                        <i class="fas fa-chart-line"></i>Analytics & Monitoring
                    </a>
                    <a class="nav-link" href="#styling">
                        <i class="fas fa-palette"></i>Styling & UI
                    </a>
                    <a class="nav-link" href="#deployment">
                        <i class="fas fa-server"></i>Deployment
                    </a>
                </div>
            </nav>

            <!-- Main Content -->
            <main class="col-lg-9 col-xl-10 content-area">
                <!-- Overview Section -->
                <section id="overview">
                    <div class="section-header">
                        <h1><i class="fas fa-file-alt me-3"></i>Document Management System</h1>
                        <p>Comprehensive Technical Documentation for ERDB AI-Powered Knowledge Management Platform</p>
                    </div>

                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h3><i class="fas fa-info-circle me-2"></i>System Overview</h3>
                                </div>
                                <div class="card-body">
                                    <p class="lead">The Document Management System is a comprehensive AI-powered platform designed for the Ecosystems Research and Development Bureau (ERDB) to manage, search, and interact with knowledge products through intelligent document processing and conversational AI capabilities.</p>

                                    <h5>Key Capabilities</h5>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i><strong>Intelligent Document Processing:</strong> PDF upload, text extraction, image analysis, and table processing</li>
                                        <li><i class="fas fa-check text-success me-2"></i><strong>AI-Powered Search:</strong> Vector-based semantic search with multiple embedding models</li>
                                        <li><i class="fas fa-check text-success me-2"></i><strong>Conversational Interface:</strong> Category-based chat with citation support and anti-hallucination modes</li>
                                        <li><i class="fas fa-check text-success me-2"></i><strong>Geographical Intelligence:</strong> Location extraction and mapping for Philippine administrative levels</li>
                                        <li><i class="fas fa-check text-success me-2"></i><strong>Comprehensive Analytics:</strong> Usage tracking, performance monitoring, and geolocation analytics</li>
                                        <li><i class="fas fa-check text-success me-2"></i><strong>Enterprise Security:</strong> Role-based access control, user management, and audit logging</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-layer-group me-2"></i>Technology Stack</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <span class="badge badge-primary me-2">Python 3.8+</span>
                                        <span class="badge badge-secondary me-2">Flask</span>
                                        <span class="badge badge-success me-2">SQLite</span>
                                    </div>
                                    <div class="mb-3">
                                        <span class="badge badge-info me-2">ChromaDB</span>
                                        <span class="badge badge-warning me-2">Ollama</span>
                                        <span class="badge badge-danger me-2">Bootstrap 5</span>
                                    </div>
                                    <div class="mb-3">
                                        <span class="badge badge-primary me-2">Leaflet.js</span>
                                        <span class="badge badge-secondary me-2">Chart.js</span>
                                        <span class="badge badge-success me-2">spaCy NLP</span>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-robot me-2"></i>AI Models</h5>
                                </div>
                                <div class="card-body">
                                    <h6>Language Models</h6>
                                    <ul class="list-unstyled small">
                                        <li><i class="fas fa-circle text-primary me-2" style="font-size: 0.5rem;"></i>Llama 3.1 8B Instruct (Default)</li>
                                        <li><i class="fas fa-circle text-secondary me-2" style="font-size: 0.5rem;"></i>Gemma 3 (1B/4B variants)</li>
                                    </ul>

                                    <h6>Vision Models</h6>
                                    <ul class="list-unstyled small">
                                        <li><i class="fas fa-circle text-success me-2" style="font-size: 0.5rem;"></i>Llama 3.2 Vision 11B</li>
                                        <li><i class="fas fa-circle text-info me-2" style="font-size: 0.5rem;"></i>Gemma 3 Multimodal (4B/12B)</li>
                                    </ul>

                                    <h6>Embedding Models</h6>
                                    <ul class="list-unstyled small">
                                        <li><i class="fas fa-circle text-warning me-2" style="font-size: 0.5rem;"></i>mxbai-embed-large (Default)</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- System Architecture Section -->
                <section id="architecture" class="mt-5">
                    <div class="section-header">
                        <h1><i class="fas fa-sitemap me-3"></i>System Architecture</h1>
                        <p>Technical architecture overview and component relationships</p>
                    </div>

                    <div class="architecture-diagram">
                        <h4 class="mb-4">High-Level Architecture</h4>
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="component-box">Frontend Layer</div>
                                <div class="component-box secondary">Bootstrap 5 UI</div>
                                <div class="component-box success">Leaflet.js Maps</div>
                                <div class="component-box info">Chart.js Analytics</div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="component-box">Flask Application Server</div>
                                <div class="component-box secondary">Authentication & RBAC</div>
                                <div class="component-box success">Session Management</div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="component-box">AI Processing Layer</div>
                                <div class="component-box secondary">Ollama Integration</div>
                                <div class="component-box success">Vision Processing</div>
                                <div class="component-box info">NLP & Location Extraction</div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="component-box">Data Storage Layer</div>
                                <div class="component-box secondary">SQLite Databases</div>
                                <div class="component-box success">ChromaDB Vectors</div>
                                <div class="component-box info">File System Storage</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-server me-2"></i>Core Components</h5>
                                </div>
                                <div class="card-body">
                                    <h6>Flask Application Server (app.py)</h6>
                                    <ul class="small">
                                        <li>HTTP request handling and routing</li>
                                        <li>Session management and authentication</li>
                                        <li>API endpoint definitions</li>
                                        <li>Template rendering and response generation</li>
                                        <li>CSRF protection with Flask-WTF</li>
                                        <li>Rate limiting with Flask-Limiter</li>
                                        <li>File upload handling (25MB limit)</li>
                                        <li>Device fingerprinting for session tracking</li>
                                        <li>Geolocation tracking middleware</li>
                                    </ul>

                                    <h6>Configuration</h6>
                                    <ul class="small">
                                        <li>Port 8080 (configurable via environment)</li>
                                        <li>Secret key management</li>
                                        <li>Maximum content length controls</li>
                                        <li>Temporary folder management</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-database me-2"></i>Database Architecture</h5>
                                </div>
                                <div class="card-body">
                                    <h6>User Management Database (user_management.db)</h6>
                                    <ul class="small">
                                        <li>User accounts and authentication</li>
                                        <li>Role-based access control</li>
                                        <li>Permission groups and inheritance</li>
                                        <li>Activity logging and audit trails</li>
                                        <li>Session management</li>
                                    </ul>

                                    <h6>Chat History Database (chat_history.db)</h6>
                                    <ul class="small">
                                        <li>Conversation history storage</li>
                                        <li>Analytics and usage tracking</li>
                                        <li>Geolocation data</li>
                                        <li>Model performance metrics</li>
                                    </ul>

                                    <h6>Content Database (content_db.sqlite)</h6>
                                    <ul class="small">
                                        <li>PDF document metadata</li>
                                        <li>URL source management</li>
                                        <li>Cover images and thumbnails</li>
                                        <li>Geographical location data</li>
                                        <li>Geocoding cache</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Core Features Section -->
                <section id="core-features" class="mt-5">
                    <div class="section-header">
                        <h1><i class="fas fa-cogs me-3"></i>Core Features</h1>
                        <p>Detailed overview of system capabilities and implementation</p>
                    </div>

                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-file-upload"></i>
                            </div>
                            <h5>Document Processing</h5>
                            <p><strong>PDF Upload & Processing:</strong></p>
                            <ul class="small">
                                <li>25MB file size limit with validation</li>
                                <li>Text extraction using PyMuPDF</li>
                                <li>Image extraction and analysis</li>
                                <li>Table detection and processing</li>
                                <li>Hierarchical storage by category</li>
                                <li>Duplicate detection and prevention</li>
                            </ul>
                            <p><strong>URL Scraping:</strong></p>
                            <ul class="small">
                                <li>Web content extraction</li>
                                <li>Image and link collection</li>
                                <li>Metadata preservation</li>
                                <li>Source URL association</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <h5>Vector Search & Retrieval</h5>
                            <p><strong>ChromaDB Integration:</strong></p>
                            <ul class="small">
                                <li>Semantic vector search</li>
                                <li>Category-based collections</li>
                                <li>Configurable retrieval parameters</li>
                                <li>Relevance threshold filtering</li>
                                <li>Metadata-enhanced search</li>
                            </ul>
                            <p><strong>Embedding Models:</strong></p>
                            <ul class="small">
                                <li>mxbai-embed-large (default)</li>
                                <li>Configurable chunk size (800 tokens)</li>
                                <li>Overlap handling (250 tokens)</li>
                                <li>Batch processing optimization</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-comments"></i>
                            </div>
                            <h5>Conversational AI</h5>
                            <p><strong>Chat Interface:</strong></p>
                            <ul class="small">
                                <li>Category-based conversations</li>
                                <li>Session management with persistence</li>
                                <li>Client name collection and addressing</li>
                                <li>Real-time response generation</li>
                                <li>Citation support with source links</li>
                            </ul>
                            <p><strong>Anti-Hallucination Modes:</strong></p>
                            <ul class="small">
                                <li><strong>Strict:</strong> Only document-based responses</li>
                                <li><strong>Balanced:</strong> Limited inference allowed</li>
                                <li><strong>Off:</strong> Creative responses enabled</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-images"></i>
                            </div>
                            <h5>Image Processing</h5>
                            <p><strong>Vision Model Analysis:</strong></p>
                            <ul class="small">
                                <li>Llama 3.2 Vision 11B integration</li>
                                <li>Gemma 3 multimodal support</li>
                                <li>Intelligent image filtering</li>
                                <li>Relevance threshold configuration</li>
                                <li>Contextual caption generation</li>
                            </ul>
                            <p><strong>Image Management:</strong></p>
                            <ul class="small">
                                <li>Cover image selection hierarchy</li>
                                <li>Thumbnail generation</li>
                                <li>URL-based image scraping</li>
                                <li>Temporary storage organization</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-table"></i>
                            </div>
                            <h5>CRUD Operations</h5>
                            <p><strong>Document Management:</strong></p>
                            <ul class="small">
                                <li>Create: PDF upload and processing</li>
                                <li>Read: Document viewing and search</li>
                                <li>Update: Metadata modification</li>
                                <li>Delete: Cascading resource cleanup</li>
                                <li>Category organization</li>
                            </ul>
                            <p><strong>Resource Management:</strong></p>
                            <ul class="small">
                                <li>Automatic cleanup on deletion</li>
                                <li>Orphaned resource detection</li>
                                <li>Storage optimization</li>
                                <li>Backup and recovery support</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-history"></i>
                            </div>
                            <h5>Session Management</h5>
                            <p><strong>Session Tracking:</strong></p>
                            <ul class="small">
                                <li>Device fingerprinting</li>
                                <li>Session persistence across refreshes</li>
                                <li>Timestamp tracking</li>
                                <li>Multi-user concurrent support</li>
                                <li>Session analytics</li>
                            </ul>
                            <p><strong>Chat History:</strong></p>
                            <ul class="small">
                                <li>Conversation storage</li>
                                <li>Source preservation</li>
                                <li>Image association</li>
                                <li>Model configuration tracking</li>
                            </ul>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-code me-2"></i>Technical Implementation Details</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <h6>File Processing Pipeline</h6>
                                    <div class="code-block">
<pre><code class="language-python">def process_pdf(pdf_path, category=None, source_url=None,
               extract_tables=True, save_images=True,
               use_vision=None, extract_locations=True):
    """
    Central PDF processing function
    - Text extraction with PyMuPDF
    - Image analysis with vision models
    - Table extraction with Camelot
    - Location extraction with spaCy NER
    - Hierarchical storage organization
    """</code></pre>
                                    </div>

                                    <h6>Query Processing</h6>
                                    <div class="code-block">
<pre><code class="language-python">def query_category(category, question,
                  anti_hallucination_mode='strict',
                  client_name=None, session_id=None):
    """
    AI query processing with:
    - Vector similarity search
    - Context assembly
    - LLM response generation
    - Citation formatting
    - Hallucination detection
    """</code></pre>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <h6>Configuration Management</h6>
                                    <div class="code-block">
<pre><code class="language-json">{
  "llm_model": "llama3.1:8b-instruct-q4_K_M",
  "embedding_model": "mxbai-embed-large:latest",
  "vision_model": "llama3.2-vision:11b-instruct-q4_K_M",
  "model_parameters": {
    "temperature": 0.7,
    "num_ctx": 4096,
    "num_predict": 256,
    "top_p": 0.9,
    "top_k": 40,
    "repeat_penalty": 1.1
  }
}</code></pre>
                                    </div>

                                    <h6>Anti-Hallucination Configuration</h6>
                                    <div class="code-block">
<pre><code class="language-json">{
  "hallucination_detection": {
    "threshold_strict": 0.6,
    "threshold_balanced": 0.4,
    "min_statement_length": 20,
    "enable_detection": true
  }
}</code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- User Management Section -->
                <section id="user-management" class="mt-5">
                    <div class="section-header">
                        <h1><i class="fas fa-users me-3"></i>User Management System</h1>
                        <p>Authentication, authorization, and user administration</p>
                    </div>

                    <div class="row">
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-shield-alt me-2"></i>Authentication & Security</h5>
                                </div>
                                <div class="card-body">
                                    <h6>User Registration & Login</h6>
                                    <ul class="small">
                                        <li>Username validation (3-20 alphanumeric characters)</li>
                                        <li>Email validation with EmailValidator</li>
                                        <li>Password complexity requirements</li>
                                        <li>Account approval workflow</li>
                                        <li>Failed login attempt tracking</li>
                                        <li>Password change enforcement</li>
                                    </ul>

                                    <h6>Security Features</h6>
                                    <ul class="small">
                                        <li>Bcrypt password hashing</li>
                                        <li>Session token management</li>
                                        <li>CSRF protection</li>
                                        <li>Rate limiting</li>
                                        <li>Account lockout protection</li>
                                        <li>Secure cookie handling</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-users-cog me-2"></i>Role-Based Access Control</h5>
                                </div>
                                <div class="card-body">
                                    <h6>User Roles</h6>
                                    <ul class="small">
                                        <li><strong>Admin:</strong> Full system access and user management</li>
                                        <li><strong>Editor:</strong> Content management and limited admin functions</li>
                                        <li><strong>Viewer:</strong> Read-only access to content</li>
                                    </ul>

                                    <h6>Permission Groups</h6>
                                    <ul class="small">
                                        <li>Function-based permissions</li>
                                        <li>Hierarchical inheritance</li>
                                        <li>Individual permission overrides</li>
                                        <li>Automatic group assignment</li>
                                        <li>Permission audit logging</li>
                                    </ul>

                                    <h6>Protected Functions</h6>
                                    <ul class="small">
                                        <li>Document upload and management</li>
                                        <li>User administration</li>
                                        <li>System configuration</li>
                                        <li>Analytics access</li>
                                        <li>Model configuration</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- AI Integration Section -->
                <section id="ai-integration" class="mt-5">
                    <div class="section-header">
                        <h1><i class="fas fa-brain me-3"></i>AI Integration</h1>
                        <p>Comprehensive AI model integration and processing capabilities</p>
                    </div>

                    <div class="row">
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-robot me-2"></i>Language Models</h5>
                                </div>
                                <div class="card-body">
                                    <h6>Primary Models</h6>
                                    <ul class="small">
                                        <li><strong>Llama 3.1 8B Instruct:</strong> Default conversational AI</li>
                                        <li><strong>Gemma 3 1B/4B:</strong> Lightweight alternatives</li>
                                        <li><strong>Model Fallback:</strong> Automatic failover to stable models</li>
                                    </ul>

                                    <h6>Model Parameters</h6>
                                    <table class="table table-sm">
                                        <tr><td>Temperature</td><td>0.7</td></tr>
                                        <tr><td>Context Window</td><td>4096</td></tr>
                                        <tr><td>Max Tokens</td><td>256</td></tr>
                                        <tr><td>Top-p</td><td>0.9</td></tr>
                                        <tr><td>Top-k</td><td>40</td></tr>
                                        <tr><td>Repeat Penalty</td><td>1.1</td></tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-eye me-2"></i>Vision Models</h5>
                                </div>
                                <div class="card-body">
                                    <h6>Supported Models</h6>
                                    <ul class="small">
                                        <li><strong>Llama 3.2 Vision 11B:</strong> Primary vision analysis</li>
                                        <li><strong>Gemma 3 4B/12B:</strong> Alternative vision capabilities</li>
                                    </ul>

                                    <h6>Vision Processing Features</h6>
                                    <ul class="small">
                                        <li>Image relevance filtering</li>
                                        <li>Contextual caption generation</li>
                                        <li>Document image analysis</li>
                                        <li>Configurable sensitivity levels</li>
                                        <li>Batch processing optimization</li>
                                        <li>Cache-enabled processing</li>
                                    </ul>

                                    <h6>Filter Sensitivity Levels</h6>
                                    <ul class="small">
                                        <li><strong>Low:</strong> Minimal filtering</li>
                                        <li><strong>Medium:</strong> Balanced relevance (default)</li>
                                        <li><strong>High:</strong> Strict relevance filtering</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-vector-square me-2"></i>Embedding Models</h5>
                                </div>
                                <div class="card-body">
                                    <h6>Primary Embedding Model</h6>
                                    <ul class="small">
                                        <li><strong>mxbai-embed-large:</strong> Default embedding model</li>
                                        <li>High-dimensional vector representations</li>
                                        <li>Optimized for semantic similarity</li>
                                        <li>Multi-language support</li>
                                    </ul>

                                    <h6>Embedding Parameters</h6>
                                    <table class="table table-sm">
                                        <tr><td>Chunk Size</td><td>800 tokens</td></tr>
                                        <tr><td>Chunk Overlap</td><td>250 tokens</td></tr>
                                        <tr><td>Batch Size</td><td>50 documents</td></tr>
                                        <tr><td>Processing Threads</td><td>4</td></tr>
                                    </table>

                                    <h6>Vector Database</h6>
                                    <ul class="small">
                                        <li>ChromaDB integration</li>
                                        <li>Category-based collections</li>
                                        <li>Metadata preservation</li>
                                        <li>Similarity search optimization</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-shield-alt me-2"></i>Anti-Hallucination System</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <h6>Detection Modes</h6>
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Mode</th>
                                                <th>Threshold</th>
                                                <th>Description</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><span class="badge badge-danger">Strict</span></td>
                                                <td>0.6</td>
                                                <td>Only document-based responses</td>
                                            </tr>
                                            <tr>
                                                <td><span class="badge badge-warning">Balanced</span></td>
                                                <td>0.4</td>
                                                <td>Limited inference with citations</td>
                                            </tr>
                                            <tr>
                                                <td><span class="badge badge-success">Off</span></td>
                                                <td>N/A</td>
                                                <td>Creative responses with external knowledge</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="col-lg-6">
                                    <h6>Detection Algorithm</h6>
                                    <div class="code-block">
<pre><code class="language-python">def detect_hallucination(answer, context_docs, mode):
    """
    Hallucination detection process:
    1. Extract key statements from answer
    2. Check statement support in context
    3. Calculate word overlap ratios
    4. Apply mode-specific thresholds
    5. Generate appropriate warnings
    """</code></pre>
                                    </div>

                                    <h6>Configuration Options</h6>
                                    <ul class="small">
                                        <li>Configurable detection thresholds</li>
                                        <li>Minimum statement length filtering</li>
                                        <li>Context overlap analysis</li>
                                        <li>Mode-specific warning messages</li>
                                        <li>Performance impact monitoring</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Geographical Features Section -->
                <section id="geographical" class="mt-5">
                    <div class="section-header">
                        <h1><i class="fas fa-map-marked-alt me-3"></i>Geographical Features</h1>
                        <p>Location extraction, mapping, and Philippine administrative level processing</p>
                    </div>

                    <div class="row">
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-search-location me-2"></i>Location Extraction (NER)</h5>
                                </div>
                                <div class="card-body">
                                    <h6>Named Entity Recognition</h6>
                                    <ul class="small">
                                        <li><strong>spaCy NLP:</strong> Core NER processing</li>
                                        <li><strong>Entity Types:</strong> GPE, LOC, FAC</li>
                                        <li><strong>Philippine Focus:</strong> Administrative level filtering</li>
                                        <li><strong>Confidence Scoring:</strong> Quality assessment</li>
                                    </ul>

                                    <h6>Administrative Levels</h6>
                                    <table class="table table-sm">
                                        <tr>
                                            <td><span class="badge badge-primary">Municipality</span></td>
                                            <td>Local government units</td>
                                        </tr>
                                        <tr>
                                            <td><span class="badge badge-secondary">City</span></td>
                                            <td>Urban administrative centers</td>
                                        </tr>
                                        <tr>
                                            <td><span class="badge badge-success">Barangay</span></td>
                                            <td>Smallest administrative divisions</td>
                                        </tr>
                                    </table>

                                    <h6>Extraction Methods</h6>
                                    <ul class="small">
                                        <li><strong>NER:</strong> Named entity recognition</li>
                                        <li><strong>Regex:</strong> Pattern-based extraction</li>
                                        <li><strong>Manual:</strong> User-defined locations</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-globe me-2"></i>Geocoding & Mapping</h5>
                                </div>
                                <div class="card-body">
                                    <h6>Geocoding Service</h6>
                                    <ul class="small">
                                        <li><strong>Nominatim (OpenStreetMap):</strong> Primary geocoding</li>
                                        <li><strong>Rate Limiting:</strong> Respectful API usage</li>
                                        <li><strong>Caching:</strong> Performance optimization</li>
                                        <li><strong>Fallback Handling:</strong> Error resilience</li>
                                    </ul>

                                    <h6>Leaflet.js Integration</h6>
                                    <ul class="small">
                                        <li>Interactive map visualization</li>
                                        <li>Clustered marker display</li>
                                        <li>Category-based filtering</li>
                                        <li>Location details sidebar</li>
                                        <li>ERDB brand color scheme</li>
                                        <li>Dark mode support</li>
                                        <li>Responsive design</li>
                                    </ul>

                                    <h6>Map Features</h6>
                                    <ul class="small">
                                        <li>Default center: Los Baños (14.1648, 121.2413)</li>
                                        <li>OpenStreetMap tile layers</li>
                                        <li>Zoom controls and attribution</li>
                                        <li>Mobile-responsive interface</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-database me-2"></i>Location Data Management</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <h6>Database Tables</h6>
                                    <div class="code-block">
<pre><code class="language-sql">-- Extracted locations storage
extracted_locations (
    id INTEGER PRIMARY KEY,
    location_text TEXT NOT NULL,
    location_type TEXT CHECK(...),
    latitude REAL,
    longitude REAL,
    confidence_score REAL,
    context_snippet TEXT,
    geocoded_address TEXT,
    country TEXT,
    region TEXT,
    city TEXT,
    municipality TEXT,
    barangay TEXT,
    administrative_level TEXT
)</code></pre>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <h6>Source Linking</h6>
                                    <div class="code-block">
<pre><code class="language-sql">-- Location source tracking
location_sources (
    id INTEGER PRIMARY KEY,
    location_id INTEGER,
    source_type TEXT CHECK(...),
    source_id INTEGER,
    page_number INTEGER,
    extraction_method TEXT,
    FOREIGN KEY (location_id)
        REFERENCES extracted_locations(id)
        ON DELETE CASCADE
)</code></pre>
                                    </div>
                                </div>
                            </div>

                            <h6>Cascading Deletion</h6>
                            <p class="small">When a PDF is deleted, all associated location data is automatically removed through foreign key constraints, ensuring data consistency and preventing orphaned records.</p>

                            <h6>Performance Optimization</h6>
                            <ul class="small">
                                <li>Geocoding cache with expiration</li>
                                <li>Confidence threshold filtering</li>
                                <li>Maximum locations per document limit</li>
                                <li>Asynchronous map loading</li>
                                <li>Batch processing for large documents</li>
                            </ul>
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <!-- Scroll to Top Button -->
    <button class="scroll-to-top" onclick="scrollToTop()" title="Scroll to Top">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Prism.js for syntax highlighting -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>

    <script>
        // Theme toggle functionality
        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.getElementById('theme-icon');

            if (body.classList.contains('dark-mode')) {
                body.classList.remove('dark-mode');
                themeIcon.classList.remove('fa-sun');
                themeIcon.classList.add('fa-moon');
                localStorage.setItem('theme', 'light');
            } else {
                body.classList.add('dark-mode');
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
                localStorage.setItem('theme', 'dark');
            }
        }

        // Load saved theme
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme');
            const themeIcon = document.getElementById('theme-icon');

            if (savedTheme === 'dark') {
                document.body.classList.add('dark-mode');
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
            }
        });

        // Sidebar toggle for mobile
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }

        // Smooth scrolling for navigation links
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.sidebar .nav-link');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all links
                    navLinks.forEach(l => l.classList.remove('active'));

                    // Add active class to clicked link
                    this.classList.add('active');

                    // Smooth scroll to target
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);

                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }

                    // Close sidebar on mobile
                    if (window.innerWidth <= 768) {
                        document.getElementById('sidebar').classList.remove('show');
                    }
                });
            });
        });

        // Scroll to top functionality
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Show/hide scroll to top button
        window.addEventListener('scroll', function() {
            const scrollButton = document.querySelector('.scroll-to-top');
            if (window.pageYOffset > 300) {
                scrollButton.classList.add('visible');
            } else {
                scrollButton.classList.remove('visible');
            }
        });

        // Update active navigation based on scroll position
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.sidebar .nav-link');

            let current = '';

            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
